/* Responsive Design */

/* Tablet Styles */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    /* Header */
    .nav {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav.active {
        display: block;
    }

    .nav-list {
        flex-direction: column;
        gap: 0;
        padding: 1rem 0;
    }

    .nav-list li {
        border-bottom: 1px solid #e2e8f0;
    }

    .nav-list a {
        display: block;
        padding: 1rem 2rem;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    /* Hero */
    .hero {
        padding: 100px 0 60px;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        text-align: center;
    }

    /* Features */
    .features {
        padding: 60px 0;
    }

    .features h2 {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    /* Tools Preview */
    .tools-preview {
        padding: 60px 0;
    }

    .tools-preview h2 {
        font-size: 2rem;
    }

    .tools-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    /* Stats */
    .stats {
        padding: 40px 0;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    /* Footer */
    .footer {
        padding: 40px 0 20px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .social-links {
        justify-content: center;
    }

    .app-links {
        justify-content: center;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    /* Header */
    .header-content {
        padding: 0.75rem 0;
    }

    .logo a {
        font-size: 1.25rem;
    }

    .logo i {
        font-size: 1.5rem;
    }

    /* Hero */
    .hero {
        padding: 80px 0 40px;
    }

    .hero h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .hero p {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    /* Features */
    .features {
        padding: 40px 0;
    }

    .features h2 {
        font-size: 1.75rem;
        margin-bottom: 2rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 1rem;
    }

    .feature-icon i {
        font-size: 1.25rem;
    }

    /* Tools Preview */
    .tools-preview {
        padding: 40px 0;
    }

    .tools-preview h2 {
        font-size: 1.75rem;
        margin-bottom: 2rem;
    }

    .tool-card {
        padding: 1.5rem;
    }

    /* Stats */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 1rem;
    }

    /* Footer */
    .footer {
        padding: 30px 0 15px;
    }

    .footer-section {
        text-align: center;
    }

    .footer-logo {
        justify-content: center;
    }

    .social-links {
        gap: 0.75rem;
    }

    .app-links {
        gap: 0.75rem;
    }

    .app-links a,
    .social-links a {
        width: 35px;
        height: 35px;
    }
}

/* Large Desktop Styles */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }

    .hero h1 {
        font-size: 4rem;
    }

    .features-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .tools-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .hero-buttons,
    .mobile-menu-toggle {
        display: none;
    }

    .hero {
        background: none;
        color: #333;
        padding: 20px 0;
    }

    .features,
    .tools-preview,
    .stats {
        padding: 20px 0;
    }

    .feature-card,
    .tool-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .hero {
        background: #000;
        color: #fff;
    }

    .btn-primary {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }

    .feature-card,
    .tool-card {
        border: 2px solid #000;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e5e5e5;
    }

    .header {
        background: #2a2a2a;
        border-bottom: 1px solid #404040;
    }

    .features {
        background: #2a2a2a;
    }

    .feature-card,
    .tool-card {
        background: #333;
        border-color: #404040;
        color: #e5e5e5;
    }

    .feature-card h3,
    .tool-card h3 {
        color: #fff;
    }

    .nav-list a {
        color: #b0b0b0;
    }

    .nav-list a:hover {
        color: #4a9eff;
    }
}
