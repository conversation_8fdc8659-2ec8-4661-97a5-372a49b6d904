/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #2563eb;
    font-size: 1.5rem;
    font-weight: 700;
}

.logo i {
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-list a {
    text-decoration: none;
    color: #64748b;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-list a:hover {
    color: #2563eb;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    margin-top: 70px;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: #fff;
    color: #2563eb;
}

.btn-primary:hover {
    background: #f8fafc;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border-color: #fff;
}

.btn-secondary:hover {
    background: #fff;
    color: #2563eb;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: #f8fafc;
}

.features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #1e293b;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: #fff;
}

.feature-card h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.feature-card p {
    color: #64748b;
    line-height: 1.6;
}

/* Tools Preview Section */
.tools-preview {
    padding: 80px 0;
}

.tools-preview h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #1e293b;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.tool-card {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.tool-card:hover {
    border-color: #2563eb;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.1);
}

.tool-icon {
    width: 50px;
    height: 50px;
    background: #eff6ff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.tool-icon i {
    font-size: 1.25rem;
    color: #2563eb;
}

.tool-card h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.tool-card p {
    color: #64748b;
    margin-bottom: 1.5rem;
}

.tool-link {
    color: #2563eb;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.tool-link:hover {
    color: #1d4ed8;
}

/* Stats Section */
.stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Footer */
.footer {
    background: #1e293b;
    color: #94a3b8;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.footer-logo i {
    margin-right: 0.5rem;
}

.footer-section h4 {
    color: #fff;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #fff;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #334155;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #2563eb;
    color: #fff;
}

.app-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.app-links a {
    width: 40px;
    height: 40px;
    background: #334155;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
    transition: all 0.3s ease;
}

.app-links a:hover {
    background: #2563eb;
    color: #fff;
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 2rem;
    text-align: center;
}

/* Page-specific Styles */

/* About Page */
.about-content {
    padding: 60px 0;
}

.content-section {
    margin-bottom: 4rem;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.content-grid.reverse {
    direction: rtl;
}

.content-grid.reverse > * {
    direction: ltr;
}

.content-text h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #1e293b;
}

.content-text p {
    margin-bottom: 1rem;
    color: #64748b;
    line-height: 1.7;
}

.values-list {
    list-style: none;
    padding: 0;
}

.values-list li {
    margin-bottom: 1rem;
    color: #64748b;
    line-height: 1.6;
}

.values-list strong {
    color: #2563eb;
}

.image-placeholder {
    width: 100%;
    height: 300px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 4rem;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #1e293b;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.team-member {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
}

.member-avatar {
    width: 80px;
    height: 80px;
    background: #f1f5f9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: #64748b;
}

.team-member h3 {
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.member-role {
    color: #2563eb;
    font-weight: 600;
    margin-bottom: 1rem;
}

.company-info {
    background: #f8fafc;
    padding: 3rem;
    border-radius: 12px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.info-item h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.info-item h3 i {
    color: #2563eb;
}

.legal-info {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 3rem;
}

.legal-content h3 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #1e293b;
}

.legal-content h3:first-child {
    margin-top: 0;
}

.about-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem;
    border-radius: 12px;
    text-align: center;
}

.about-stats h2 {
    color: white;
    margin-bottom: 2rem;
}

/* Contact Page */
.contact-content {
    padding: 60px 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    margin-bottom: 4rem;
}

.contact-form {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.contact-info {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 12px;
}

.contact-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: #2563eb;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.contact-details h3 {
    margin-bottom: 0.5rem;
    color: #1e293b;
}

.faq-section,
.support-section {
    margin-top: 4rem;
}

.faq-grid,
.support-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.faq-item,
.support-option {
    background: #fff;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.faq-item h3 {
    margin-bottom: 1rem;
    color: #1e293b;
}

.support-icon {
    width: 60px;
    height: 60px;
    background: #eff6ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: #2563eb;
}

/* Blog Page */
.blog-content {
    padding: 60px 0;
}

.featured-article {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    background: #fff;
    border-radius: 12px;
    padding: 3rem;
    margin-bottom: 4rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.article-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.category {
    background: #2563eb;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.date {
    color: #64748b;
    font-size: 0.875rem;
}

.featured-article h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    line-height: 1.3;
}

.featured-article h2 a {
    color: #1e293b;
    text-decoration: none;
    transition: color 0.3s ease;
}

.featured-article h2 a:hover {
    color: #2563eb;
}

.featured-article p {
    color: #64748b;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.read-more {
    color: #2563eb;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: #1d4ed8;
}

.blog-categories {
    margin-bottom: 3rem;
}

.blog-categories h3 {
    margin-bottom: 1rem;
    color: #1e293b;
}

.category-tags {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.category-tag {
    background: #f1f5f9;
    color: #64748b;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.category-tag:hover,
.category-tag.active {
    background: #2563eb;
    color: white;
}

.blog-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.blog-post {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-post:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.post-image {
    height: 200px;
    overflow: hidden;
}

.post-image .image-placeholder {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
}

.post-content {
    padding: 1.5rem;
}

.post-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.post-content h3 {
    margin-bottom: 1rem;
    line-height: 1.4;
}

.post-content h3 a {
    color: #1e293b;
    text-decoration: none;
    transition: color 0.3s ease;
}

.post-content h3 a:hover {
    color: #2563eb;
}

.post-content p {
    color: #64748b;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.newsletter-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 3rem;
}

.newsletter-section h2 {
    color: white;
    margin-bottom: 1rem;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
    margin-top: 2rem;
}

.newsletter-form input {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    outline: none;
}

.newsletter-form .btn {
    white-space: nowrap;
}

.popular-tags h3 {
    margin-bottom: 1rem;
    color: #1e293b;
}

.tags-cloud {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.tag {
    background: #f1f5f9;
    color: #64748b;
    padding: 6px 12px;
    border-radius: 16px;
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.tag:hover {
    background: #2563eb;
    color: white;
}

/* Legal Pages */
.legal-content {
    padding: 60px 0;
}

.legal-document {
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.document-meta {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border-left: 4px solid #2563eb;
}

.document-meta p {
    margin: 0;
    color: #64748b;
    font-size: 0.9rem;
}

.legal-document h2 {
    color: #1e293b;
    margin-top: 2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e2e8f0;
}

.legal-document h2:first-of-type {
    margin-top: 0;
}

.legal-document h3 {
    color: #374151;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
}

.legal-document p {
    margin-bottom: 1rem;
    line-height: 1.7;
    color: #4b5563;
}

.legal-document ul {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.legal-document li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
    color: #4b5563;
}

.legal-document a {
    color: #2563eb;
    text-decoration: none;
}

.legal-document a:hover {
    text-decoration: underline;
}

.policy-footer {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    margin-top: 2rem;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.policy-footer em {
    color: #64748b;
    font-style: italic;
}

/* Article Pages */
.article-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 60px;
    margin-top: 70px;
}

.breadcrumb {
    margin-bottom: 1rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.breadcrumb a {
    color: white;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.article-header .article-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.article-header .category {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
}

.article-header .date,
.article-header .reading-time {
    color: rgba(255,255,255,0.8);
    font-size: 0.875rem;
}

.article-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.article-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.author-details {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.author-title {
    font-size: 0.875rem;
    opacity: 0.8;
}

.article-content {
    padding: 60px 0;
}

.article-body {
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.8;
}

.article-body h2 {
    color: #1e293b;
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    font-size: 1.75rem;
}

.article-body h2:first-child {
    margin-top: 0;
}

.article-body p {
    margin-bottom: 1.5rem;
    color: #374151;
}

.article-body strong {
    color: #1e293b;
}

.article-footer {
    max-width: 800px;
    margin: 3rem auto 0;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.article-tags,
.article-share {
    margin-bottom: 2rem;
}

.article-tags h4,
.article-share h4 {
    margin-bottom: 1rem;
    color: #1e293b;
}

.share-buttons {
    display: flex;
    gap: 1rem;
}

.share-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.share-btn:hover {
    transform: translateY(-2px);
}

.share-btn.twitter { background: #1da1f2; }
.share-btn.facebook { background: #4267b2; }
.share-btn.linkedin { background: #0077b5; }
.share-btn.copy { background: #64748b; }

.related-articles {
    max-width: 800px;
    margin: 3rem auto 0;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

.related-articles h3 {
    margin-bottom: 2rem;
    color: #1e293b;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.related-article {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.related-article h4 {
    margin-bottom: 0.75rem;
}

.related-article h4 a {
    color: #1e293b;
    text-decoration: none;
    transition: color 0.3s ease;
}

.related-article h4 a:hover {
    color: #2563eb;
}

.related-article p {
    color: #64748b;
    font-size: 0.9rem;
    margin: 0;
}

/* Notification System */
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: #fff;
    border-radius: 8px;
    padding: 1rem 1.5rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    z-index: 10000;
    max-width: 400px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    animation: slideIn 0.3s ease;
}

.notification-success {
    border-left: 4px solid #10b981;
    background: #f0fdf4;
    color: #065f46;
}

.notification-error {
    border-left: 4px solid #ef4444;
    background: #fef2f2;
    color: #991b1b;
}

.notification-info {
    border-left: 4px solid #3b82f6;
    background: #eff6ff;
    color: #1e40af;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Form Focus States */
.form-group.focused label {
    color: #2563eb;
}

.form-group.focused input,
.form-group.focused select,
.form-group.focused textarea {
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group.has-value label {
    font-weight: 600;
}

/* Character Counter */
.character-counter {
    font-size: 0.875rem;
    color: #64748b;
    text-align: right;
    margin-top: 0.5rem;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Active Navigation */
.nav-list a.active {
    color: #2563eb;
    font-weight: 600;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }

.hidden { display: none; }
.visible { display: block; }

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
*:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

/* Skip Link */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #2563eb;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10001;
}

.skip-link:focus {
    top: 6px;
}
