<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolPlanet - Test Page</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container" style="padding: 2rem 0;">
        <h1>ToolPlanet - Test Page</h1>
        
        <div style="margin: 2rem 0;">
            <h2>Quick Tests</h2>
            
            <div style="margin: 1rem 0;">
                <button id="test-notification" class="btn btn-primary">Test Notification</button>
                <button id="test-image-api" class="btn btn-secondary">Test Image API</button>
                <button id="test-text-api" class="btn btn-secondary">Test Text API</button>
            </div>
            
            <div style="margin: 2rem 0;">
                <h3>Navigation Links Test</h3>
                <ul>
                    <li><a href="index.html">Home Page</a></li>
                    <li><a href="tools.html">Tools Page</a></li>
                    <li><a href="blog.html">Blog Page</a></li>
                    <li><a href="about.html">About Page</a></li>
                    <li><a href="contact.html">Contact Page</a></li>
                    <li><a href="privacy.html">Privacy Policy</a></li>
                    <li><a href="terms.html">Terms of Service</a></li>
                    <li><a href="blog/ai-revolution-2025.html">Sample Blog Article</a></li>
                </ul>
            </div>
            
            <div style="margin: 2rem 0;">
                <h3>Responsive Design Test</h3>
                <p>Resize your browser window to test responsive design. The layout should adapt to different screen sizes.</p>
            </div>
            
            <div style="margin: 2rem 0;">
                <h3>API Test Results</h3>
                <div id="test-results" style="background: #f8fafc; padding: 1rem; border-radius: 8px; min-height: 100px;">
                    Test results will appear here...
                </div>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('test-results');
            
            // Test notification system
            document.getElementById('test-notification').addEventListener('click', function() {
                if (window.ToolPlanet && window.ToolPlanet.showNotification) {
                    window.ToolPlanet.showNotification('Test notification working!', 'success');
                    testResults.innerHTML += '<p>✅ Notification system working</p>';
                } else {
                    testResults.innerHTML += '<p>❌ Notification system not loaded</p>';
                }
            });
            
            // Test image API
            document.getElementById('test-image-api').addEventListener('click', async function() {
                try {
                    const testPrompt = 'a beautiful sunset';
                    const imageUrl = `https://image.pollinations.ai/prompt/${encodeURIComponent(testPrompt)}?width=400&height=300&nologo=true`;
                    
                    // Test if image loads
                    const img = new Image();
                    img.onload = function() {
                        testResults.innerHTML += '<p>✅ Image API working - ' + imageUrl + '</p>';
                    };
                    img.onerror = function() {
                        testResults.innerHTML += '<p>❌ Image API failed</p>';
                    };
                    img.src = imageUrl;
                    
                } catch (error) {
                    testResults.innerHTML += '<p>❌ Image API error: ' + error.message + '</p>';
                }
            });
            
            // Test text API
            document.getElementById('test-text-api').addEventListener('click', async function() {
                try {
                    const response = await fetch('https://text.pollinations.ai/Hello, this is a test');
                    if (response.ok) {
                        const text = await response.text();
                        testResults.innerHTML += '<p>✅ Text API working - Response: ' + text.substring(0, 100) + '...</p>';
                    } else {
                        testResults.innerHTML += '<p>❌ Text API failed with status: ' + response.status + '</p>';
                    }
                } catch (error) {
                    testResults.innerHTML += '<p>❌ Text API error: ' + error.message + '</p>';
                }
            });
            
            // Test CSS loading
            const computedStyle = window.getComputedStyle(document.body);
            if (computedStyle.fontFamily.includes('Inter')) {
                testResults.innerHTML += '<p>✅ CSS and fonts loaded correctly</p>';
            } else {
                testResults.innerHTML += '<p>❌ CSS or fonts not loaded properly</p>';
            }
            
            // Test JavaScript functionality
            if (typeof window.ToolPlanet === 'object') {
                testResults.innerHTML += '<p>✅ Main JavaScript loaded</p>';
            } else {
                testResults.innerHTML += '<p>❌ Main JavaScript not loaded</p>';
            }
        });
    </script>
</body>
</html>
