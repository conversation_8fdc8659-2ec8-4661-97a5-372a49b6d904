// Contact page functionality

document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                subject: formData.get('subject'),
                message: formData.get('message'),
                newsletter: formData.get('newsletter') === 'on'
            };
            
            // Validate form
            if (!validateContactForm(data)) {
                return;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.dataset.originalText || submitBtn.textContent;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            submitBtn.disabled = true;
            
            // Simulate form submission (replace with actual API call)
            setTimeout(() => {
                // Reset form
                this.reset();
                
                // Show success message
                window.ToolPlanet.showNotification('Thank you for your message! We\'ll get back to you soon.', 'success');
                
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                
                // If newsletter subscription was checked, show additional message
                if (data.newsletter) {
                    setTimeout(() => {
                        window.ToolPlanet.showNotification('You\'ve been subscribed to our newsletter!', 'info');
                    }, 2000);
                }
            }, 2000);
        });
    }
    
    function validateContactForm(data) {
        // Name validation
        if (!data.name || data.name.trim().length < 2) {
            window.ToolPlanet.showNotification('Please enter a valid name (at least 2 characters)', 'error');
            return false;
        }
        
        // Email validation
        if (!window.ToolPlanet.validateEmail(data.email)) {
            window.ToolPlanet.showNotification('Please enter a valid email address', 'error');
            return false;
        }
        
        // Subject validation
        if (!data.subject) {
            window.ToolPlanet.showNotification('Please select a subject', 'error');
            return false;
        }
        
        // Message validation
        if (!data.message || data.message.trim().length < 10) {
            window.ToolPlanet.showNotification('Please enter a message (at least 10 characters)', 'error');
            return false;
        }
        
        return true;
    }
    
    // FAQ accordion functionality
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
        const question = item.querySelector('h3');
        if (question) {
            question.style.cursor = 'pointer';
            question.addEventListener('click', function() {
                const answer = item.querySelector('p');
                if (answer) {
                    const isVisible = answer.style.display !== 'none';
                    answer.style.display = isVisible ? 'none' : 'block';
                    
                    // Add visual indicator
                    const icon = question.querySelector('i') || document.createElement('i');
                    if (!question.querySelector('i')) {
                        icon.className = 'fas fa-chevron-down';
                        icon.style.marginLeft = '0.5rem';
                        icon.style.fontSize = '0.8rem';
                        question.appendChild(icon);
                    }
                    
                    icon.className = isVisible ? 'fas fa-chevron-down' : 'fas fa-chevron-up';
                }
            });
        }
    });
    
    // Auto-resize textarea
    const messageTextarea = document.getElementById('message');
    if (messageTextarea) {
        messageTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    }
    
    // Character counter for message field
    if (messageTextarea) {
        const maxLength = 1000;
        const counter = document.createElement('div');
        counter.className = 'character-counter';
        counter.style.textAlign = 'right';
        counter.style.fontSize = '0.875rem';
        counter.style.color = '#64748b';
        counter.style.marginTop = '0.5rem';
        
        messageTextarea.parentNode.appendChild(counter);
        
        function updateCounter() {
            const remaining = maxLength - messageTextarea.value.length;
            counter.textContent = `${remaining} characters remaining`;
            
            if (remaining < 50) {
                counter.style.color = '#ef4444';
            } else if (remaining < 100) {
                counter.style.color = '#f59e0b';
            } else {
                counter.style.color = '#64748b';
            }
        }
        
        messageTextarea.addEventListener('input', updateCounter);
        updateCounter();
    }
    
    // Form field focus effects
    const formFields = document.querySelectorAll('.form-group input, .form-group select, .form-group textarea');
    formFields.forEach(field => {
        field.addEventListener('focus', function() {
            this.parentNode.classList.add('focused');
        });
        
        field.addEventListener('blur', function() {
            this.parentNode.classList.remove('focused');
            if (this.value.trim()) {
                this.parentNode.classList.add('has-value');
            } else {
                this.parentNode.classList.remove('has-value');
            }
        });
        
        // Check initial value
        if (field.value.trim()) {
            field.parentNode.classList.add('has-value');
        }
    });
});
