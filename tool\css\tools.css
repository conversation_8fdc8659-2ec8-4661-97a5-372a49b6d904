/* Tools Page Specific Styles */

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 60px;
    margin-top: 70px;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.tools-section {
    padding: 60px 0;
}

/* Tool Categories */
.tool-categories {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.category-btn {
    background: #f1f5f9;
    border: 2px solid #e2e8f0;
    color: #64748b;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.category-btn:hover,
.category-btn.active {
    background: #2563eb;
    border-color: #2563eb;
    color: white;
}

/* Tool Container */
.tool-container {
    background: #fff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    margin-bottom: 3rem;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.tool-container:hover {
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.tool-header {
    background: #f8fafc;
    padding: 2rem;
    border-bottom: 1px solid #e2e8f0;
}

.tool-header h2 {
    color: #1e293b;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.tool-header h2 i {
    color: #2563eb;
}

.tool-header p {
    color: #64748b;
    margin: 0;
}

.tool-content {
    padding: 2rem;
}

/* Input Sections */
.tool-input-section {
    margin-bottom: 2rem;
}

.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.input-group input,
.input-group textarea,
.input-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group textarea:focus,
.input-group select:focus {
    outline: none;
    border-color: #2563eb;
}

.input-group textarea {
    resize: vertical;
    min-height: 80px;
}

.input-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

/* Output Sections */
.tool-output-section {
    border-top: 1px solid #e2e8f0;
    padding-top: 2rem;
}

.output-area {
    min-height: 200px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.output-area.has-content {
    border-style: solid;
    border-color: #2563eb;
}

.output-area .placeholder {
    text-align: center;
    color: #9ca3af;
}

.output-area .placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.output-area img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 6px;
}

.output-area .text-content {
    padding: 1rem;
    background: #f9fafb;
    border-radius: 6px;
    width: 100%;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
}

.output-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Chat Specific Styles */
.chat-container {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    height: 500px;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background: #f9fafb;
}

.message {
    display: flex;
    margin-bottom: 1rem;
    align-items: flex-start;
    gap: 0.75rem;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: #2563eb;
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background: #10b981;
    color: white;
}

.message-content {
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    max-width: 70%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.user-message .message-content {
    background: #2563eb;
    color: white;
}

.chat-input-container {
    padding: 1rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 0.75rem;
}

.chat-input-container input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 25px;
    outline: none;
}

.chat-input-container input:focus {
    border-color: #2563eb;
}

.chat-input-container .btn {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2563eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* QR Code Specific */
.qr-code-container {
    text-align: center;
    padding: 1rem;
}

.qr-code-container canvas {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

/* Responsive Design for Tools */
@media (max-width: 768px) {
    .page-header {
        padding: 100px 0 40px;
    }

    .page-header h1 {
        font-size: 2.5rem;
    }

    .tool-content {
        padding: 1.5rem;
    }

    .tool-header {
        padding: 1.5rem;
    }

    .input-row {
        grid-template-columns: 1fr;
    }

    .output-actions {
        flex-direction: column;
    }

    .chat-container {
        height: 400px;
    }

    .message-content {
        max-width: 85%;
    }

    .tool-categories {
        gap: 0.5rem;
    }

    .category-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .page-header h1 {
        font-size: 2rem;
    }

    .tool-content {
        padding: 1rem;
    }

    .tool-header {
        padding: 1rem;
    }

    .tool-header h2 {
        font-size: 1.25rem;
    }

    .chat-container {
        height: 350px;
    }
}
