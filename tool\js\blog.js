// Blog page functionality

document.addEventListener('DOMContentLoaded', function() {
    // Category filtering
    const categoryTags = document.querySelectorAll('.category-tag');
    const blogPosts = document.querySelectorAll('.blog-post');
    
    categoryTags.forEach(tag => {
        tag.addEventListener('click', function(e) {
            e.preventDefault();
            
            const category = this.dataset.category;
            
            // Update active tag
            categoryTags.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // Filter posts
            blogPosts.forEach(post => {
                if (category === 'all' || post.dataset.category === category) {
                    post.style.display = 'block';
                    // Add fade-in animation
                    post.style.opacity = '0';
                    setTimeout(() => {
                        post.style.opacity = '1';
                        post.style.transition = 'opacity 0.3s ease';
                    }, 100);
                } else {
                    post.style.display = 'none';
                }
            });
            
            // Update URL without page reload
            const url = new URL(window.location);
            if (category === 'all') {
                url.searchParams.delete('category');
            } else {
                url.searchParams.set('category', category);
            }
            window.history.pushState({}, '', url);
        });
    });
    
    // Load category from URL on page load
    const urlParams = new URLSearchParams(window.location.search);
    const categoryFromUrl = urlParams.get('category');
    if (categoryFromUrl) {
        const targetTag = document.querySelector(`[data-category="${categoryFromUrl}"]`);
        if (targetTag) {
            targetTag.click();
        }
    }
    
    // Newsletter subscription
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const emailInput = this.querySelector('input[type="email"]');
            const email = emailInput.value.trim();
            
            if (!window.ToolPlanet.validateEmail(email)) {
                window.ToolPlanet.showNotification('Please enter a valid email address', 'error');
                return;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Subscribing...';
            submitBtn.disabled = true;
            
            // Simulate subscription (replace with actual API call)
            setTimeout(() => {
                emailInput.value = '';
                window.ToolPlanet.showNotification('Thank you for subscribing to our newsletter!', 'success');
                
                // Reset button
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });
    }
    
    // Tag cloud interactions
    const tags = document.querySelectorAll('.tag');
    tags.forEach(tag => {
        tag.addEventListener('click', function(e) {
            e.preventDefault();
            
            const tagText = this.textContent.toLowerCase();
            
            // Filter posts based on tag (simple text matching)
            let foundPosts = 0;
            blogPosts.forEach(post => {
                const postContent = post.textContent.toLowerCase();
                if (postContent.includes(tagText)) {
                    post.style.display = 'block';
                    foundPosts++;
                } else {
                    post.style.display = 'none';
                }
            });
            
            // Update category tags
            categoryTags.forEach(t => t.classList.remove('active'));
            
            // Show notification
            window.ToolPlanet.showNotification(`Found ${foundPosts} posts related to "${this.textContent}"`, 'info');
            
            // Scroll to posts
            document.querySelector('.blog-posts-grid').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        });
    });
    
    // Search functionality (if search input exists)
    const searchInput = document.querySelector('#blog-search');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.toLowerCase().trim();
            
            searchTimeout = setTimeout(() => {
                if (query.length > 2) {
                    performSearch(query);
                } else {
                    // Show all posts
                    blogPosts.forEach(post => {
                        post.style.display = 'block';
                    });
                }
            }, 300);
        });
    }
    
    function performSearch(query) {
        let foundPosts = 0;
        
        blogPosts.forEach(post => {
            const title = post.querySelector('h3').textContent.toLowerCase();
            const content = post.querySelector('p').textContent.toLowerCase();
            const category = post.dataset.category.toLowerCase();
            
            if (title.includes(query) || content.includes(query) || category.includes(query)) {
                post.style.display = 'block';
                foundPosts++;
                
                // Highlight search terms (simple implementation)
                highlightSearchTerms(post, query);
            } else {
                post.style.display = 'none';
            }
        });
        
        // Show search results count
        let resultsMessage = document.querySelector('.search-results-message');
        if (!resultsMessage) {
            resultsMessage = document.createElement('div');
            resultsMessage.className = 'search-results-message';
            resultsMessage.style.textAlign = 'center';
            resultsMessage.style.margin = '1rem 0';
            resultsMessage.style.color = '#64748b';
            document.querySelector('.blog-posts-grid').parentNode.insertBefore(resultsMessage, document.querySelector('.blog-posts-grid'));
        }
        
        if (foundPosts === 0) {
            resultsMessage.textContent = `No posts found for "${query}". Try a different search term.`;
        } else {
            resultsMessage.textContent = `Found ${foundPosts} post${foundPosts === 1 ? '' : 's'} for "${query}"`;
        }
    }
    
    function highlightSearchTerms(post, query) {
        // Simple highlighting - in a real implementation, you'd want more sophisticated highlighting
        const title = post.querySelector('h3 a');
        const content = post.querySelector('p');
        
        [title, content].forEach(element => {
            if (element) {
                const text = element.textContent;
                const regex = new RegExp(`(${query})`, 'gi');
                const highlightedText = text.replace(regex, '<mark>$1</mark>');
                element.innerHTML = highlightedText;
            }
        });
    }
    
    // Reading time estimation
    blogPosts.forEach(post => {
        const content = post.querySelector('p').textContent;
        const wordCount = content.split(' ').length;
        const readingTime = Math.ceil(wordCount / 200); // Assuming 200 words per minute
        
        const readingTimeElement = document.createElement('span');
        readingTimeElement.className = 'reading-time';
        readingTimeElement.textContent = `${readingTime} min read`;
        readingTimeElement.style.color = '#64748b';
        readingTimeElement.style.fontSize = '0.875rem';
        
        const metaSection = post.querySelector('.post-meta');
        if (metaSection) {
            metaSection.appendChild(readingTimeElement);
        }
    });
    
    // Infinite scroll (basic implementation)
    let isLoading = false;
    
    window.addEventListener('scroll', function() {
        if (isLoading) return;
        
        const scrollPosition = window.innerHeight + window.scrollY;
        const documentHeight = document.documentElement.offsetHeight;
        
        if (scrollPosition >= documentHeight - 1000) {
            loadMorePosts();
        }
    });
    
    function loadMorePosts() {
        isLoading = true;
        
        // Simulate loading more posts
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'loading-indicator';
        loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading more posts...';
        loadingIndicator.style.textAlign = 'center';
        loadingIndicator.style.padding = '2rem';
        loadingIndicator.style.color = '#64748b';
        
        document.querySelector('.blog-posts-grid').parentNode.appendChild(loadingIndicator);
        
        setTimeout(() => {
            loadingIndicator.remove();
            isLoading = false;
            // In a real implementation, you would load actual posts here
        }, 2000);
    }
});
