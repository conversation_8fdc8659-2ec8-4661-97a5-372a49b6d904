// Tools functionality for ToolPlanet

document.addEventListener('DOMContentLoaded', function() {
    // Tool category filtering
    const categoryButtons = document.querySelectorAll('.category-btn');
    const toolContainers = document.querySelectorAll('.tool-container');

    categoryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // Update active button
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter tools
            toolContainers.forEach(container => {
                if (category === 'all' || container.dataset.category === category) {
                    container.style.display = 'block';
                } else {
                    container.style.display = 'none';
                }
            });
        });
    });

    // Image Generator
    const imageGeneratorBtn = document.getElementById('generate-image-btn');
    const imagePrompt = document.getElementById('image-prompt');
    const imageModel = document.getElementById('image-model');
    const imageSize = document.getElementById('image-size');
    const imageOutput = document.getElementById('image-output');
    const downloadImageBtn = document.getElementById('download-image-btn');
    const copyImageUrlBtn = document.getElementById('copy-image-url-btn');

    if (imageGeneratorBtn) {
        imageGeneratorBtn.addEventListener('click', async function() {
            const prompt = imagePrompt.value.trim();
            if (!prompt) {
                window.ToolPlanet.showNotification('Please enter a prompt for the image', 'error');
                return;
            }

            this.classList.add('loading');
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';

            try {
                const encodedPrompt = encodeURIComponent(prompt);
                const model = imageModel.value;
                const size = imageSize.value;
                
                // Using Pollinations AI API
                const imageUrl = `https://image.pollinations.ai/prompt/${encodedPrompt}?model=${model}&width=${size.split('x')[0]}&height=${size.split('x')[1]}&nologo=true`;
                
                // Create image element
                const img = document.createElement('img');
                img.src = imageUrl;
                img.alt = prompt;
                img.style.maxWidth = '100%';
                img.style.height = 'auto';
                
                // Clear output and add image
                imageOutput.innerHTML = '';
                imageOutput.appendChild(img);
                imageOutput.classList.add('has-content');
                
                // Show action buttons
                const outputActions = imageOutput.parentElement.querySelector('.output-actions');
                outputActions.style.display = 'flex';
                
                // Set up download functionality
                downloadImageBtn.onclick = () => downloadImage(imageUrl, `generated-image-${Date.now()}.png`);
                copyImageUrlBtn.onclick = () => window.ToolPlanet.copyToClipboard(imageUrl);
                
                window.ToolPlanet.showNotification('Image generated successfully!', 'success');
                
            } catch (error) {
                console.error('Error generating image:', error);
                window.ToolPlanet.showNotification('Failed to generate image. Please try again.', 'error');
            } finally {
                this.classList.remove('loading');
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-magic"></i> Generate Image';
            }
        });
    }

    // Text Generator
    const textGeneratorBtn = document.getElementById('generate-text-btn');
    const textPrompt = document.getElementById('text-prompt');
    const textModel = document.getElementById('text-model');
    const textLength = document.getElementById('text-length');
    const textOutput = document.getElementById('text-output');
    const copyTextBtn = document.getElementById('copy-text-btn');
    const downloadTextBtn = document.getElementById('download-text-btn');

    if (textGeneratorBtn) {
        textGeneratorBtn.addEventListener('click', async function() {
            const prompt = textPrompt.value.trim();
            if (!prompt) {
                window.ToolPlanet.showNotification('Please enter a prompt for text generation', 'error');
                return;
            }

            this.classList.add('loading');
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating...';

            try {
                // Using Pollinations AI Text API
                const response = await fetch('https://text.pollinations.ai/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        messages: [
                            {
                                role: 'user',
                                content: prompt
                            }
                        ],
                        model: textModel.value,
                        max_tokens: getMaxTokensForLength(textLength.value)
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to generate text');
                }

                const data = await response.text();
                
                // Create text content div
                const textContent = document.createElement('div');
                textContent.className = 'text-content';
                textContent.textContent = data;
                
                // Clear output and add text
                textOutput.innerHTML = '';
                textOutput.appendChild(textContent);
                textOutput.classList.add('has-content');
                
                // Show action buttons
                const outputActions = textOutput.parentElement.querySelector('.output-actions');
                outputActions.style.display = 'flex';
                
                // Set up action functionality
                copyTextBtn.onclick = () => window.ToolPlanet.copyToClipboard(data);
                downloadTextBtn.onclick = () => downloadText(data, `generated-text-${Date.now()}.txt`);
                
                window.ToolPlanet.showNotification('Text generated successfully!', 'success');
                
            } catch (error) {
                console.error('Error generating text:', error);
                window.ToolPlanet.showNotification('Failed to generate text. Please try again.', 'error');
            } finally {
                this.classList.remove('loading');
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-pen"></i> Generate Text';
            }
        });
    }

    // Chat Assistant
    const chatInput = document.getElementById('chat-input');
    const sendChatBtn = document.getElementById('send-chat-btn');
    const chatMessages = document.getElementById('chat-messages');

    function addMessage(content, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'bot-message'}`;
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas ${isUser ? 'fa-user' : 'fa-robot'}"></i>
            </div>
            <div class="message-content">
                <p>${content}</p>
            </div>
        `;
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    async function sendChatMessage() {
        const message = chatInput.value.trim();
        if (!message) return;

        // Add user message
        addMessage(message, true);
        chatInput.value = '';
        
        // Show typing indicator
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot-message typing';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <p>Typing...</p>
            </div>
        `;
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        try {
            // Using Pollinations AI Text API for chat
            const response = await fetch(`https://text.pollinations.ai/${encodeURIComponent(message)}`);
            const botResponse = await response.text();
            
            // Remove typing indicator
            typingDiv.remove();
            
            // Add bot response
            addMessage(botResponse);
            
        } catch (error) {
            console.error('Error in chat:', error);
            typingDiv.remove();
            addMessage('Sorry, I encountered an error. Please try again.');
        }
    }

    if (sendChatBtn && chatInput) {
        sendChatBtn.addEventListener('click', sendChatMessage);
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendChatMessage();
            }
        });
    }

    // QR Code Generator
    const qrGeneratorBtn = document.getElementById('generate-qr-btn');
    const qrData = document.getElementById('qr-data');
    const qrSize = document.getElementById('qr-size');
    const qrColor = document.getElementById('qr-color');
    const qrOutput = document.getElementById('qr-output');
    const downloadQrBtn = document.getElementById('download-qr-btn');

    if (qrGeneratorBtn) {
        qrGeneratorBtn.addEventListener('click', function() {
            const data = qrData.value.trim();
            if (!data) {
                window.ToolPlanet.showNotification('Please enter data to encode', 'error');
                return;
            }

            const size = qrSize.value;
            const color = qrColor.value.replace('#', '');
            
            // Using QR Server API
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(data)}&color=${color}`;
            
            // Create QR code image
            const img = document.createElement('img');
            img.src = qrUrl;
            img.alt = 'QR Code';
            img.style.maxWidth = '100%';
            
            // Clear output and add QR code
            qrOutput.innerHTML = '';
            const container = document.createElement('div');
            container.className = 'qr-code-container';
            container.appendChild(img);
            qrOutput.appendChild(container);
            qrOutput.classList.add('has-content');
            
            // Show download button
            const outputActions = qrOutput.parentElement.querySelector('.output-actions');
            outputActions.style.display = 'flex';
            
            // Set up download functionality
            downloadQrBtn.onclick = () => downloadImage(qrUrl, `qr-code-${Date.now()}.png`);
            
            window.ToolPlanet.showNotification('QR code generated successfully!', 'success');
        });
    }

    // Utility functions
    function getMaxTokensForLength(length) {
        switch (length) {
            case 'short': return 200;
            case 'medium': return 500;
            case 'long': return 1000;
            default: return 500;
        }
    }

    function downloadImage(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    function downloadText(text, filename) {
        const blob = new Blob([text], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
});
